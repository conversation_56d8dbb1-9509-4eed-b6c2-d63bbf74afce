{"name": "factcheck-app", "version": "1.0.0", "description": "FactCheck - A web application for verifying news credibility", "scripts": {"setup": "setup.bat", "setup:unix": "chmod +x setup.sh && ./setup.sh", "dev": "start-dev.bat", "dev:unix": "chmod +x start-dev.sh && ./start-dev.sh", "restart-all": "node restart-all.js", "restart-all:windows": "restart-all.bat", "restart-all:unix": "chmod +x restart-all.sh && ./restart-all.sh", "kill-all": "node restart-all.js --kill-only", "kill-all:windows": "kill-all.bat", "build": "cd client && npm run build", "deploy": "deploy.bat", "deploy:unix": "chmod +x deploy.sh && ./deploy.sh", "emulators": "firebase emulators:start", "firestore:init": "node scripts/init-firestore.js", "firestore:deploy-indexes": "./scripts/deploy-indexes.sh", "firestore:deploy-rules": "firebase deploy --only firestore:rules", "firestore:setup": "npm run firestore:deploy-indexes && npm run firestore:deploy-rules && npm run firestore:init", "firestore:view": "node scripts/view-firestore-data.js", "chat:test": "node scripts/test-chat-api.js", "chat:fix": "node scripts/fix-chat-system.js", "chat:setup-production": "node scripts/setup-production-chat.js", "chat:deploy": "node scripts/setup-production-chat.js --skip-deploy", "production:fix-openai": "node scripts/fix-production-openai.js", "production:free-plan": "node scripts/setup-free-plan-chat.js", "deploy:force": "node scripts/force-deploy-no-cache.js", "server": "cd server && npm run dev", "client": "cd client && npm start", "functions": "cd functions && npm run serve", "test": "npm run test:server && npm run test:client", "test:server": "cd server && npm test", "test:client": "cd client && npm test", "lint": "npm run lint:server && npm run lint:client && npm run lint:functions", "lint:server": "cd server && npm run lint", "lint:client": "cd client && npm run lint", "lint:functions": "cd functions && npm run lint", "install:all": "npm install && cd server && npm install && cd ../client && npm install && cd ../functions && npm install"}, "keywords": ["factcheck", "news", "credibility", "verification", "react", "express", "firebase"], "author": "FactCheck Team", "license": "MIT", "dependencies": {"@firebasegen/default-connector": "file:dataconnect-generated/js/default-connector", "axios": "^1.9.0", "dotenv": "^16.5.0", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "openai": "^5.1.1"}, "devDependencies": {"concurrently": "^8.2.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}