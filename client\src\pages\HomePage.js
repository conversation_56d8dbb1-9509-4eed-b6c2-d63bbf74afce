import React from 'react';
import { Search, Shield, Users, BarChart3, BookOpen, MessageCircle } from 'lucide-react';
import TrendingArticles from '../components/TrendingArticles';
import AnimatedStats from '../components/AnimatedStats';
import { ActionCard, FeatureCard } from '../components/ui/StandardCard';
import { ResponsiveContainer, Section } from '../components/ui/ResponsiveLayout';
import EnhancedHeroSection from '../components/hero/EnhancedHeroSection';
import ScrollTriggeredSection from '../components/animations/ScrollTriggeredSection';
import PageTransition from '../components/transitions/PageTransition';
import FloatingWidgets from '../components/widgets/FloatingWidgets';

const HomePage = () => {
  const features = [
    {
      icon: Search,
      title: 'Kiểm Tra Link',
      description: 'Kiểm tra ngay độ tin cậy của bài viết tin tức và nguồn thông tin với hệ thống xác minh tiên tiến.'
    },
    {
      icon: Shield,
      title: '<PERSON><PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON><PERSON> thông tin từ các tổ chức báo chí uy tín và các tổ chức kiểm chứng sự thật để đảm bảo độ chính xác.'
    },
    {
      icon: BarChart3,
      title: 'Chấm Điểm Tin Cậy',
      description: 'Nhận điểm số độ tin cậy chi tiết và phân tích để giúp bạn đưa ra quyết định thông tin chính xác.'
    },
    {
      icon: Users,
      title: 'Cộng Đồng',
      description: 'Tham gia cộng đồng kiểm chứng sự thật và cùng nhau chống lại thông tin sai lệch.'
    }
  ];

  return (
    <PageTransition>
      {/* Floating Widgets */}
      <FloatingWidgets />

      {/* Enhanced Hero Section */}
      <EnhancedHeroSection />

      {/* Main Content Section - Enterprise Layout */}
      <Section className="py-24 bg-white dark:bg-gray-900">
        <ResponsiveContainer size="xl">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
              Hoạt động cộng đồng
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Tham gia vào hệ sinh thái kiểm chứng thông tin toàn diện với các công cụ và tính năng tiên tiến
            </p>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-4 gap-8 lg:gap-12">
            {/* Main Action Cards */}
            <div className="xl:col-span-3">
              <ScrollTriggeredSection
                animation="popIn"
                stagger={0.15}
                className="grid grid-cols-1 md:grid-cols-2 gap-8"
              >
                <ActionCard
                  icon={Users}
                  title="Cộng đồng kiểm tin"
                  description="Tham gia cùng cộng đồng đánh giá và xác minh thông tin với hệ thống voting thông minh"
                  color="blue"
                  onClick={() => window.location.href = '/community'}
                />

                <ActionCard
                  icon={BookOpen}
                  title="Kiến thức nền"
                  description="Học cách nhận biết và kiểm tra thông tin sai lệch qua các khóa học chuyên sâu"
                  color="green"
                  onClick={() => window.location.href = '/knowledge'}
                />

                <ActionCard
                  icon={MessageCircle}
                  title="Trợ lý AI"
                  description="Hỏi đáp với AI về cách kiểm tra thông tin sử dụng công nghệ Gemini tiên tiến"
                  color="purple"
                  onClick={() => window.location.href = '/chat'}
                />

                <ActionCard
                  icon={Search}
                  title="Gửi bài viết"
                  description="Chia sẻ bài viết để cộng đồng cùng đánh giá với hệ thống phân tích đa chiều"
                  color="orange"
                  onClick={() => window.location.href = '/submit'}
                />
              </ScrollTriggeredSection>
            </div>

            {/* Enhanced Sidebar */}
            <div className="xl:col-span-1">
              <div className="sticky top-8">
                <TrendingArticles />
              </div>
            </div>
          </div>
        </ResponsiveContainer>
      </Section>

      {/* Animated Statistics Section */}
      <AnimatedStats />

      {/* Features Section - Enterprise Design */}
      <Section className="py-24 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800">
        <ResponsiveContainer size="xl">
          <ScrollTriggeredSection animation="scaleIn" stagger={0.1}>
            {/* Section Header */}
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
                Tại Sao Chọn FactCheck?
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Nền tảng kiểm chứng thông tin hàng đầu với công nghệ AI tiên tiến và cộng đồng chuyên gia
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-8 lg:gap-10">
              {features.map((feature, index) => (
                <FeatureCard
                  key={feature.title}
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                  color={index % 2 === 0 ? "blue" : "purple"}
                  className="transform hover:scale-105 transition-all duration-300"
                />
              ))}
            </div>

            {/* Call to Action */}
            <div className="text-center mt-16">
              <div className="inline-flex items-center gap-4 px-8 py-4 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
                <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
                  Sẵn sàng bắt đầu?
                </span>
                <button
                  onClick={() => window.location.href = '/check'}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  Kiểm tra ngay
                </button>
              </div>
            </div>
          </ScrollTriggeredSection>
        </ResponsiveContainer>
      </Section>
    </PageTransition>
  );
};

export default HomePage;
