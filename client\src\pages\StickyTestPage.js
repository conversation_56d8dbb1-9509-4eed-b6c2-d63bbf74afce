import React from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>ointer, 
  Eye, 
  Zap,
  Scroll,
  Menu,
  MessageCircle,
  Plus
} from 'lucide-react';
import StickyHamburgerMenu from '../components/navigation/StickyHamburgerMenu';
import FloatingWidgets from '../components/widgets/FloatingWidgets';

const StickyTestPage = () => {
  const sections = [
    {
      id: 'hero',
      title: 'Sticky Behavior Test',
      subtitle: 'Scroll để xem widgets follow theo',
      bg: 'bg-gradient-to-br from-blue-500 to-purple-600',
      icon: Scroll,
      instructions: [
        'Scroll xuống để thấy hamburger menu compact',
        'Widgets sẽ follow theo scroll với parallax effect',
        'Fast scroll để thấy velocity-based animations'
      ]
    },
    {
      id: 'section1',
      title: 'Section 1',
      subtitle: 'Widgets đang follow scroll position',
      bg: 'bg-gradient-to-br from-green-500 to-teal-600',
      icon: Eye,
      instructions: [
        'Hamburger menu ở top-right đang compact',
        'Chat widget ở bottom-right đang follow scroll',
        'Plus widget có radial menu khi click'
      ]
    },
    {
      id: 'section2',
      title: 'Section 2',
      subtitle: 'Fast scroll test',
      bg: 'bg-gradient-to-br from-purple-500 to-pink-600',
      icon: Zap,
      instructions: [
        'Scroll nhanh xuống để thấy widgets slide down',
        'Scroll nhanh lên để thấy widgets bounce up',
        'Velocity detection tạo dynamic animations'
      ]
    },
    {
      id: 'section3',
      title: 'Section 3',
      subtitle: 'Hover effects test',
      bg: 'bg-gradient-to-br from-orange-500 to-red-600',
      icon: MousePointer,
      instructions: [
        'Hover hamburger menu để thấy rotation',
        'Hover widgets để thấy scale effects',
        'All animations respect reduced motion'
      ]
    },
    {
      id: 'section4',
      title: 'Section 4',
      subtitle: 'Auto-hide behavior',
      bg: 'bg-gradient-to-br from-indigo-500 to-blue-600',
      icon: Menu,
      instructions: [
        'Stop scrolling để thấy auto-fade effect',
        'Widgets fade sau 3-4 giây không hoạt động',
        'Hover để restore full opacity'
      ]
    },
    {
      id: 'section5',
      title: 'Section 5',
      subtitle: 'Scroll-to-top test',
      bg: 'bg-gradient-to-br from-pink-500 to-purple-600',
      icon: ArrowDown,
      instructions: [
        'Scroll-to-top button đã xuất hiện',
        'Click để smooth scroll về đầu trang',
        'Button có bounce entrance animation'
      ]
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Sticky Components */}
      <StickyHamburgerMenu />
      <FloatingWidgets />

      {/* Test Sections */}
      {sections.map((section, index) => {
        const Icon = section.icon;
        return (
          <section
            key={section.id}
            id={section.id}
            className={`min-h-screen ${section.bg} flex items-center justify-center text-white relative overflow-hidden`}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25% 25%, white 2px, transparent 2px)`,
                backgroundSize: '50px 50px'
              }} />
            </div>

            {/* Content */}
            <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-8"
              >
                <Icon size={80} className="mx-auto mb-6 opacity-80" />
                <h1 className="text-5xl md:text-7xl font-bold mb-4">
                  {section.title}
                </h1>
                <p className="text-xl md:text-2xl opacity-90 mb-8">
                  {section.subtitle}
                </p>
              </motion.div>

              {/* Instructions */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8"
              >
                <h3 className="text-xl font-semibold mb-4">Test Instructions:</h3>
                <ul className="text-left space-y-2">
                  {section.instructions.map((instruction, i) => (
                    <li key={i} className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-white rounded-full mt-2 flex-shrink-0" />
                      <span className="text-lg opacity-90">{instruction}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>

              {/* Scroll Indicator */}
              {index < sections.length - 1 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="flex flex-col items-center space-y-2"
                >
                  <span className="text-sm opacity-70">Scroll để tiếp tục test</span>
                  <ArrowDown size={24} className="animate-bounce opacity-70" />
                </motion.div>
              )}

              {/* Floating Particles */}
              <div className="absolute inset-0 pointer-events-none">
                {Array.from({ length: 15 }, (_, i) => (
                  <div
                    key={i}
                    className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
                    style={{
                      top: `${Math.random() * 100}%`,
                      left: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 3}s`,
                      animationDuration: `${2 + Math.random() * 2}s`
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Section Number */}
            <div className="absolute top-8 left-8 text-6xl font-bold opacity-20">
              {String(index + 1).padStart(2, '0')}
            </div>
          </section>
        );
      })}

      {/* Final Section - Summary */}
      <section className="min-h-screen bg-gradient-to-br from-gray-800 to-black flex items-center justify-center text-white">
        <div className="text-center max-w-4xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-6xl font-bold mb-6">Test Complete! 🎉</h1>
            <p className="text-2xl opacity-90 mb-8">
              Sticky behavior system đang hoạt động hoàn hảo
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <Menu className="mr-3" size={24} />
                  Hamburger Menu
                </h3>
                <ul className="text-left space-y-2 text-sm opacity-90">
                  <li>✅ Compacts on scroll > 100px</li>
                  <li>✅ Follows scroll with parallax</li>
                  <li>✅ Auto-fades after 3s inactivity</li>
                  <li>✅ Velocity-based animations</li>
                  <li>✅ Hover rotation effects</li>
                </ul>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                <h3 className="text-xl font-semibold mb-4 flex items-center">
                  <MessageCircle className="mr-3" size={24} />
                  Floating Widgets
                </h3>
                <ul className="text-left space-y-2 text-sm opacity-90">
                  <li>✅ Follow scroll position</li>
                  <li>✅ Auto-hide after 4s</li>
                  <li>✅ Show on scroll up</li>
                  <li>✅ Floating animation</li>
                  <li>✅ Expandable radial menu</li>
                </ul>
              </div>
            </div>

            <div className="mt-12">
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200"
              >
                Scroll to Top để test lại
              </button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default StickyTestPage;
