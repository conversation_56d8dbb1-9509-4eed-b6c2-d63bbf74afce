import React, { useRef } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  Eye, 
  Zap, 
  ArrowUp,
  Menu,
  MessageCircle,
  Plus,
  Settings,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import { useStickyMenu, useFloatingWidget, useScrollToTop, useMagnetic } from '../hooks/useStickyBehavior';
import TabNavigation from '../components/navigation/TabNavigation';

const StickyBehaviorDemo = () => {
  // Demo refs using custom hooks
  const stickyMenuRef = useStickyMenu({
    compactOnScroll: true,
    compactThreshold: 200,
    fadeOnInactive: true,
    magnetic: true
  });

  const floatingWidgetRef = useFloatingWidget({
    autoHide: true,
    autoHideDelay: 3000,
    showOnScrollUp: true
  });

  const scrollTopRef = useScrollToTop(400);
  const magneticRef = useMagnetic(0.5);

  const demoSections = [
    {
      id: 'hero',
      title: 'Sticky Behavior Demo',
      subtitle: 'GSAP-powered floating elements',
      height: 'h-screen',
      bg: 'bg-gradient-to-br from-blue-500 to-purple-600'
    },
    {
      id: 'features',
      title: 'Sticky Features',
      subtitle: 'Advanced scroll behaviors',
      height: 'h-screen',
      bg: 'bg-gradient-to-br from-green-500 to-teal-600'
    },
    {
      id: 'responsive',
      title: 'Responsive Design',
      subtitle: 'Optimized for all devices',
      height: 'h-screen',
      bg: 'bg-gradient-to-br from-purple-500 to-pink-600'
    },
    {
      id: 'performance',
      title: 'Performance',
      subtitle: 'Hardware accelerated',
      height: 'h-screen',
      bg: 'bg-gradient-to-br from-orange-500 to-red-600'
    },
    {
      id: 'accessibility',
      title: 'Accessibility',
      subtitle: 'Reduced motion support',
      height: 'h-screen',
      bg: 'bg-gradient-to-br from-indigo-500 to-blue-600'
    }
  ];

  const features = [
    {
      icon: Menu,
      title: 'Sticky Hamburger Menu',
      description: 'Compacts on scroll, fades on inactivity, magnetic hover effect',
      behaviors: ['Compact on scroll > 100px', 'Fade after 3s inactivity', 'Magnetic attraction', 'Hover rotation']
    },
    {
      icon: MessageCircle,
      title: 'Floating Chat Widget',
      description: 'Auto-hide behavior, scroll velocity detection, entrance animation',
      behaviors: ['Auto-hide after 4s', 'Show on scroll up', 'Floating animation', 'Scale on hover']
    },
    {
      icon: ArrowUp,
      title: 'Scroll to Top',
      description: 'Appears after threshold, smooth scroll animation, feedback effects',
      behaviors: ['Show after 300px scroll', 'Smooth scroll to top', 'Scale feedback', 'Bounce entrance']
    },
    {
      icon: Zap,
      title: 'Magnetic Effects',
      description: 'Mouse attraction, elastic return, performance optimized',
      behaviors: ['Mouse follow effect', 'Elastic return animation', 'Hardware accelerated', 'Touch-friendly']
    }
  ];

  const deviceOptimizations = [
    {
      icon: Monitor,
      device: 'Desktop',
      optimizations: ['Complex hover effects', 'Parallax animations', 'Magnetic interactions', 'Full feature set']
    },
    {
      icon: Tablet,
      device: 'Tablet',
      optimizations: ['Moderate animations', 'Touch-optimized', 'Reduced complexity', 'Battery efficient']
    },
    {
      icon: Smartphone,
      device: 'Mobile',
      optimizations: ['Minimal animations', 'Touch gestures', 'Performance focused', 'Simplified UI']
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <TabNavigation />

      {/* Demo Sticky Menu */}
      <div
        ref={stickyMenuRef}
        className="fixed top-4 left-4 z-50 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center cursor-pointer shadow-lg"
        title="Demo Sticky Menu"
      >
        <Menu size={20} />
      </div>

      {/* Demo Floating Widget */}
      <div
        ref={floatingWidgetRef}
        className="fixed bottom-20 right-6 z-40 w-14 h-14 bg-green-600 text-white rounded-full flex items-center justify-center cursor-pointer shadow-lg"
        title="Demo Floating Widget"
      >
        <MessageCircle size={24} />
      </div>

      {/* Demo Scroll to Top */}
      <div
        ref={scrollTopRef}
        className="fixed bottom-6 right-6 z-40 w-12 h-12 bg-gray-600 text-white rounded-full flex items-center justify-center cursor-pointer shadow-lg"
        title="Scroll to Top"
      >
        <ArrowUp size={20} />
      </div>

      {/* Demo Magnetic Element */}
      <div
        ref={magneticRef}
        className="fixed top-4 right-20 z-50 w-16 h-16 bg-purple-600 text-white rounded-full flex items-center justify-center cursor-pointer shadow-lg"
        title="Magnetic Element"
      >
        <MousePointer size={24} />
      </div>

      {/* Demo Sections */}
      {demoSections.map((section, index) => (
        <section
          key={section.id}
          id={section.id}
          className={`${section.height} ${section.bg} flex items-center justify-center text-white relative overflow-hidden`}
        >
          <div className="text-center z-10">
            <motion.h1
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-5xl md:text-7xl font-bold mb-4"
            >
              {section.title}
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl md:text-2xl opacity-90"
            >
              {section.subtitle}
            </motion.p>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="mt-8 text-6xl opacity-50"
            >
              {index === 0 && <Scroll />}
              {index === 1 && <Eye />}
              {index === 2 && <Smartphone />}
              {index === 3 && <Zap />}
              {index === 4 && <Settings />}
            </motion.div>
          </div>

          {/* Floating particles */}
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 10 }, (_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-white/20 rounded-full animate-pulse"
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`
                }}
              />
            ))}
          </div>
        </section>
      ))}

      {/* Features Section */}
      <section className="py-20 bg-white dark:bg-gray-800">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Sticky Behavior Features
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400">
              Advanced GSAP-powered interactions
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 bg-blue-600 text-white rounded-lg flex items-center justify-center">
                      <Icon size={24} />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                  
                  <ul className="space-y-2">
                    {feature.behaviors.map((behavior, i) => (
                      <li key={i} className="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full" />
                        <span>{behavior}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Device Optimizations */}
      <section className="py-20 bg-gray-100 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Responsive Optimizations
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-400">
              Tailored animations for every device
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {deviceOptimizations.map((device, index) => {
              const Icon = device.icon;
              return (
                <motion.div
                  key={device.device}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="bg-white dark:bg-gray-800 rounded-lg p-6 text-center hover:shadow-lg transition-shadow"
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon size={32} />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    {device.device}
                  </h3>
                  
                  <ul className="space-y-2">
                    {device.optimizations.map((optimization, i) => (
                      <li key={i} className="text-sm text-gray-600 dark:text-gray-400">
                        {optimization}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Instructions */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-8">Try the Demo!</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
            <div>
              <h3 className="text-xl font-semibold mb-4">Scroll Behaviors:</h3>
              <ul className="space-y-2">
                <li>• Scroll down to see menu compact</li>
                <li>• Stop scrolling to see fade effect</li>
                <li>• Scroll up fast to see widgets appear</li>
                <li>• Scroll past 400px to see scroll-to-top</li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-4">Hover Effects:</h3>
              <ul className="space-y-2">
                <li>• Hover over purple magnetic element</li>
                <li>• Try the sticky menu hover rotation</li>
                <li>• Hover widgets for scale effects</li>
                <li>• All animations respect reduced motion</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default StickyBehaviorDemo;
